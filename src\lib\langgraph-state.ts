import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { Annotation, MemorySaver, messagesStateReducer, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { dataCollectionNode } from '../app/api/langgraph/analysis/agents/data_collection';
import { fundamentalAnalystNode } from '../app/api/langgraph/analysis/agents/fundamental_analyst';
import { newsAnalystNode } from '../app/api/langgraph/analysis/agents/news_analyst';
import { sentimentAnalystNode } from '../app/api/langgraph/analysis/agents/sentiment_analyst';
import { technicalAnalystNode } from '../app/api/langgraph/analysis/agents/technical_analyst';
import { toolNode, tools } from './langgraph-tools';

// 根据 design-langgraph.md 的详细状态定义
export const TradingAgentAnnotation = Annotation.Root({
  ticker: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => '',
  }),
  date: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => new Date().toISOString().split('T')[0],
  }),
  config: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({
      deepThinkLLM: 'gpt-4o',
      quickThinkLLM: 'gpt-4o-mini',
      maxDebateRounds: 3,
      researchDepth: 'standard',
      onlineTools: true,
    }),
  }),
  data: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({}),
  }),
  analysis: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({}),
  }),
  research: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({ debateRounds: [] }),
  }),
  risk: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  decision: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  currentStage: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => 'start',
  }),
  messages: Annotation({
    reducer: messagesStateReducer,
    default: () => [],
  }),
  status: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => 'pending',
  }),
  progress: Annotation({
    reducer: (x: number, y?: number) => y ?? x,
    default: () => 0,
  }),
});

// 判断是否继续执行的函数
export function shouldContinue(state: typeof TradingAgentAnnotation.State) {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  if (lastMessage.tool_calls?.length) {
    return 'tools';
  }
  return '__end__';
}

// 调用模型的函数
export async function callModel(state: typeof TradingAgentAnnotation.State) {
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
  const OPENAI_BASE_URL =
    process.env.OPENAI_BASE_URL ||
    process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
    'https://api.openai.com/v1';
  const model = new ChatOpenAI({
    modelName: 'gpt-4o-mini',
    temperature: 0,
    openAIApiKey: OPENAI_API_KEY,
    configuration: {
      baseURL: OPENAI_BASE_URL,
    },
  }).bindTools(tools);

  // 构建系统提示
  const systemPrompt = `你是一个专业的金融交易分析师。你的任务是：\n1. 分析股票的基本面、技术面和新闻情绪\n2. 评估投资风险\n3. 提供明确的交易建议\n\n当前分析的股票代码是: ${state.ticker}\n\n请使用可用的工具来收集和分析数据，然后提供专业的交易建议。`;

  const messages = [new HumanMessage(systemPrompt), ...state.messages];

  const response = await model.invoke(messages);
  return { messages: [response] };
}

// 新的综合节点
export async function summarizeAnalysisNode(state: typeof TradingAgentAnnotation.State) {
  const { fundamental, technical, sentiment, news } = state.analysis;

  let summary = '分析摘要:\n';
  if (fundamental) summary += `\n[基本面分析]\n${fundamental.summary}\n`;
  if (technical) summary += `\n[技术分析]\n${technical.summary}\n`;
  if (sentiment) summary += `\n[情绪分析]\n${sentiment.summary}\n`;
  if (news) summary += `\n[新闻摘要]\n${news.summary}\n`;

  return { messages: [new HumanMessage(summary)] };
}

// 研究团队占位符节点
export async function researchNode(state: typeof TradingAgentAnnotation.State) {
  console.log('[Research Team] Performing deep dive...');
  const researchSummary =
    '研究团队对初步分析进行了深入探讨，认为技术指标中的看涨信号值得关注，但基本面数据喜忧参半。';
  const messages = [...state.messages, new AIMessage(researchSummary)];
  return { messages };
}

// 风险评估占位符节点
export async function riskAssessmentNode(state: typeof TradingAgentAnnotation.State) {
  console.log('[Risk Team] Assessing risks...');
  const riskSummary =
    '风险评估团队识别出主要风险点：市场波动性增加、行业竞争加剧。建议设置严格的止损位。';
  const finalPrompt = `${
    state.messages[state.messages.length - 1].content
  }\n\n${riskSummary}\n\n请综合以上所有信息，提供最终的交易决策。`;
  const messages = [...state.messages, new AIMessage(riskSummary), new HumanMessage(finalPrompt)];
  return { messages };
}

// 创建交易分析工作流
export function createTradingWorkflow() {
  const workflow = new StateGraph(TradingAgentAnnotation)
    // 阶段 1: 数据收集
    .addNode('data_collection', dataCollectionNode)

    // 阶段 2: 并行分析师
    .addNode('fundamental_analyst', fundamentalAnalystNode)
    .addNode('technical_analyst', technicalAnalystNode)
    .addNode('sentiment_analyst', sentimentAnalystNode)
    .addNode('news_analyst', newsAnalystNode)

    // 阶段 3: 综合
    .addNode('summarize', summarizeAnalysisNode)

    // 阶段 3: 研究
    .addNode('research', researchNode)

    // 阶段 4: 风险评估
    .addNode('risk_assessment', riskAssessmentNode)

    // 阶段 5: 最终决策
    .addNode('agent', callModel)
    .addNode('tools', toolNode);

  // 定义工作流路径
  // 从起点开始数据收集
  workflow.addEdge('__start__', 'data_collection');

  // 数据收集完成后，并行启动所有分析师
  workflow.addEdge('data_collection', 'fundamental_analyst');
  workflow.addEdge('data_collection', 'technical_analyst');
  workflow.addEdge('data_collection', 'sentiment_analyst');
  workflow.addEdge('data_collection', 'news_analyst');

  // 所有分析师完成后，进入综合阶段
  // 通过将 summarize 设置为它们的下游，LangGraph 会自动等待所有上游节点完成
  workflow.addEdge('fundamental_analyst', 'summarize');
  workflow.addEdge('technical_analyst', 'summarize');
  workflow.addEdge('sentiment_analyst', 'summarize');
  workflow.addEdge('news_analyst', 'summarize');

  // 顺序执行后续阶段
  workflow.addEdge('summarize', 'research');
  workflow.addEdge('research', 'risk_assessment');
  workflow.addEdge('risk_assessment', 'agent');

  // 保留最终决策节点的工具调用循环
  workflow.addEdge('tools', 'agent');
  workflow.addConditionalEdges('agent', shouldContinue);

  const checkpointer = new MemorySaver();
  return workflow.compile({ checkpointer });
}
